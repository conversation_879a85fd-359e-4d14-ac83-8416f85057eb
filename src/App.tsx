import { useState, useEffect } from 'react';
import { CardStack } from './components/CardStack/CardStack';
import { Header } from './components/Layout/Header';
import { useCardStore } from './store/cardStore';
import { philosophyAPI } from './services/api';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';

function App() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { cards, setCards, currentIndex } = useCardStore();

  useEffect(() => {
    const fetchDailyCards = async () => {
      try {
        setLoading(true);
        const response = await philosophyAPI.getDailyCards();
        
        if (response.success) {
          setCards(response.data);
        } else {
          setError(response.message || '加载失败');
        }
      } catch (err) {
        setError('网络连接失败');
        console.error('Error fetching cards:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDailyCards();
  }, [setCards]);

  const getCurrentDate = () => {
    return new Date().toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 flex items-center justify-center">
        <motion.div
          className="text-center text-white"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p className="text-lg">正在为您准备今日的哲学思考...</p>
        </motion.div>
      </div>
    );
  }

  if (error || cards.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 flex items-center justify-center">
        <motion.div
          className="text-center text-white max-w-md mx-auto p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h2 className="text-2xl font-bold mb-4">哎呀，出了点问题</h2>
          <p className="text-lg opacity-80 mb-6">
            {error || '暂时无法加载哲学卡片'}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
          >
            重新加载
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 overflow-hidden">
      <div className="container mx-auto px-4 py-8 h-screen flex flex-col">
        <Header
          currentDate={getCurrentDate()}
          totalCards={cards.length}
          currentIndex={currentIndex}
        />
        
        <div className="flex-1 flex items-center justify-center">
          <CardStack cards={cards} />
        </div>

        {/* Footer */}
        <motion.footer
          className="text-center text-white/60 text-sm py-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          每日十张哲学卡片，开启智慧之旅
        </motion.footer>
      </div>
    </div>
  );
}

export default App;
