import { create } from 'zustand';
import type { PhilosophyCard } from '../types';

interface CardStore {
  cards: PhilosophyCard[];
  currentIndex: number;
  favoriteCards: string[];
  readCards: string[];
  setCards: (cards: PhilosophyCard[]) => void;
  nextCard: () => void;
  prevCard: () => void;
  toggleFavorite: (cardId: string) => void;
  markAsRead: (cardId: string) => void;
  resetDaily: () => void;
}

export const useCardStore = create<CardStore>((set) => ({
  cards: [],
  currentIndex: 0,
  favoriteCards: JSON.parse(localStorage.getItem('favoriteCards') || '[]'),
  readCards: JSON.parse(localStorage.getItem('readCards') || '[]'),
  
  setCards: (cards) => set({ cards, currentIndex: 0 }),
  
  nextCard: () => set((state) => ({ 
    currentIndex: Math.min(state.currentIndex + 1, state.cards.length - 1) 
  })),
  
  prevCard: () => set((state) => ({ 
    currentIndex: Math.max(state.currentIndex - 1, 0) 
  })),
  
  toggleFavorite: (cardId) => set((state) => {
    const newFavorites = state.favoriteCards.includes(cardId)
      ? state.favoriteCards.filter(id => id !== cardId)
      : [...state.favoriteCards, cardId];
    
    localStorage.setItem('favoriteCards', JSON.stringify(newFavorites));
    return { favoriteCards: newFavorites };
  }),
  
  markAsRead: (cardId) => set((state) => {
    const newReadCards = [...state.readCards, cardId];
    localStorage.setItem('readCards', JSON.stringify(newReadCards));
    return { readCards: newReadCards };
  }),
  
  resetDaily: () => set(() => {
    localStorage.removeItem('readCards');
    return { readCards: [], currentIndex: 0 };
  }),
}));