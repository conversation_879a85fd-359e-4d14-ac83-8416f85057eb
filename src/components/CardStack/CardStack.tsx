import React, { useState } from 'react';
import { motion, useAnimation } from 'framer-motion';
import type { PanInfo } from 'framer-motion';
import { PhilosophyCard } from '../Card/PhilosophyCard';
import type { PhilosophyCard as CardType } from '../../types';
import { useCardStore } from '../../store/cardStore';

interface CardStackProps {
  cards: CardType[];
}

export const CardStack: React.FC<CardStackProps> = ({ cards }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [draggedCard, setDraggedCard] = useState<string | null>(null);
  const controls = useAnimation();
  
  const { favoriteCards, toggleFavorite, markAsRead } = useCardStore();

  const handleDragEnd = async (
    _event: MouseEvent | TouchEvent | PointerEvent,
    info: PanInfo,
    cardId: string
  ) => {
    const threshold = 100;
    const { offset, velocity } = info;

    if (Math.abs(offset.x) > threshold || Math.abs(velocity.x) > 500) {
      const direction = offset.x > 0 ? 'right' : 'left';
      
      await controls.start({
        x: direction === 'right' ? 300 : -300,
        opacity: 0,
        transition: { duration: 0.3 }
      });

      markAsRead(cardId);
      setCurrentIndex(prev => Math.min(prev + 1, cards.length - 1));
      
      await controls.start({
        x: 0,
        opacity: 1,
        transition: { duration: 0 }
      });
    } else {
      controls.start({
        x: 0,
        transition: { type: 'spring', stiffness: 500, damping: 30 }
      });
    }
    
    setDraggedCard(null);
  };

  const handleShare = (card: CardType) => {
    if (navigator.share) {
      navigator.share({
        title: card.title,
        text: `${card.content} — ${card.author}`,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(`${card.title}\n\n${card.content}\n\n— ${card.author}`);
    }
  };

  const visibleCards = cards.slice(currentIndex, currentIndex + 3);

  return (
    <div className="relative w-full h-full flex items-center justify-center">
      <div className="relative">
        {visibleCards.map((card, index) => {
          const isTop = index === 0;
          const zIndex = visibleCards.length - index;
          const scale = 1 - index * 0.05;
          const yOffset = index * 10;

          return (
            <motion.div
              key={card.id}
              className="absolute"
              style={{ zIndex }}
              initial={{ scale, y: yOffset }}
              animate={{ 
                scale: draggedCard === card.id ? 1.05 : scale, 
                y: yOffset,
                rotateZ: draggedCard === card.id ? 0 : index * 2
              }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            >
              <motion.div
                drag={isTop ? 'x' : false}
                dragConstraints={{ left: -200, right: 200 }}
                dragElastic={0.1}
                onDragStart={() => setDraggedCard(card.id)}
                onDragEnd={(event, info) => handleDragEnd(event, info, card.id)}
                animate={controls}
                whileDrag={{ cursor: 'grabbing' }}
                className={isTop ? 'cursor-grab' : 'cursor-default'}
              >
                <PhilosophyCard
                  card={card}
                  onFavorite={toggleFavorite}
                  onShare={handleShare}
                  isFavorited={favoriteCards.includes(card.id)}
                  className={!isTop ? 'pointer-events-none' : ''}
                />
              </motion.div>
            </motion.div>
          );
        })}
      </div>

      {/* Progress indicator */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
        <div className="flex space-x-2">
          {cards.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                index <= currentIndex ? 'bg-white' : 'bg-white/30'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Swipe hint */}
      {currentIndex === 0 && (
        <motion.div
          className="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white text-sm opacity-70"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 0.7, y: 0 }}
          transition={{ delay: 1 }}
        >
          左右滑动切换卡片
        </motion.div>
      )}
    </div>
  );
};