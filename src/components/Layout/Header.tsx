import React from 'react';
import { motion } from 'framer-motion';
import { BookO<PERSON>, Heart, Calendar } from 'lucide-react';

interface HeaderProps {
  currentDate: string;
  totalCards: number;
  currentIndex: number;
}

export const Header: React.FC<HeaderProps> = ({ currentDate, totalCards, currentIndex }) => {
  return (
    <motion.header
      className="w-full max-w-md mx-auto p-6 text-white"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-center mb-6">
        <motion.div
          className="inline-flex items-center space-x-2 mb-2"
          whileHover={{ scale: 1.05 }}
        >
          <BookOpen className="w-8 h-8" />
          <h1 className="text-2xl font-bold">哲学日课</h1>
        </motion.div>
        
        <div className="flex items-center justify-center space-x-4 text-sm opacity-80">
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4" />
            <span>{currentDate}</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <Heart className="w-4 h-4" />
            <span>{currentIndex + 1}/{totalCards}</span>
          </div>
        </div>
      </div>
    </motion.header>
  );
};