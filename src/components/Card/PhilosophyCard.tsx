import React from 'react';
import { motion } from 'framer-motion';
import { Heart, Share2, BookOpen } from 'lucide-react';
import type { PhilosophyCard as CardType } from '../../types';

interface PhilosophyCardProps {
  card: CardType;
  onFavorite: (cardId: string) => void;
  onShare: (card: CardType) => void;
  isFavorited: boolean;
  className?: string;
}

export const PhilosophyCard: React.FC<PhilosophyCardProps> = ({
  card,
  onFavorite,
  onShare,
  isFavorited,
  className = ''
}) => {
  const bgGradients = [
    'bg-gradient-to-br from-purple-400 to-purple-600',
    'bg-gradient-to-br from-blue-400 to-blue-600',
    'bg-gradient-to-br from-green-400 to-green-600',
    'bg-gradient-to-br from-pink-400 to-pink-600',
    'bg-gradient-to-br from-indigo-400 to-indigo-600',
  ];

  const gradientClass = bgGradients[parseInt(card.id) % bgGradients.length];

  return (
    <motion.div
      className={`relative w-80 h-96 mx-auto rounded-2xl shadow-2xl overflow-hidden ${gradientClass} ${className}`}
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3 }}
    >
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-32 h-32 rounded-full bg-white transform -translate-x-16 -translate-y-16" />
        <div className="absolute bottom-0 right-0 w-24 h-24 rounded-full bg-white transform translate-x-12 translate-y-12" />
      </div>

      {/* Content */}
      <div className="relative p-6 h-full flex flex-col justify-between text-white">
        <div>
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium bg-white/20 px-3 py-1 rounded-full">
              {card.category}
            </span>
            <BookOpen className="w-5 h-5 opacity-60" />
          </div>

          <h2 className="text-xl font-bold mb-4 leading-tight">
            {card.title}
          </h2>

          {card.quote && (
            <blockquote className="text-sm italic mb-4 opacity-90 border-l-2 border-white/30 pl-3">
              "{card.quote}"
            </blockquote>
          )}

          <p className="text-sm leading-relaxed opacity-90 line-clamp-6">
            {card.content}
          </p>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm font-medium opacity-80">
            — {card.author}
          </span>
          
          <div className="flex space-x-3">
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={() => onFavorite(card.id)}
              className={`p-2 rounded-full transition-colors ${
                isFavorited 
                  ? 'bg-red-500 text-white' 
                  : 'bg-white/20 hover:bg-white/30'
              }`}
            >
              <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
            </motion.button>
            
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={() => onShare(card)}
              className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
            >
              <Share2 className="w-4 h-4" />
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};