export interface PhilosophyCard {
  id: string;
  title: string;
  content: string;
  author: string;
  category: string;
  quote?: string;
  backgroundColor?: string;
  createdAt: string;
}

export interface CardStackProps {
  cards: PhilosophyCard[];
  onCardSwipe: (cardId: string, direction: 'left' | 'right') => void;
  onCardClick: (card: PhilosophyCard) => void;
}

export interface APIResponse {
  success: boolean;
  data: PhilosophyCard[];
  message?: string;
}