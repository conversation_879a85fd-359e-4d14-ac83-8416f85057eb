import type { APIResponse, PhilosophyCard } from '../types';
import { mockPhilosophyCards } from './mockData';

interface ChatCompletionMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface ChatCompletionResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

class PhilosophyAPI {
  private _baseURL = 'https://api.302.ai/v1';
  private _apiKey = 'sk-ODyFBC8pMKRfgftmq6lVyKQ28aWMdUQuwngEPs3BbBUH9vds';
  private _model = '302-agent-meirizhexue';

  async getDailyCards(): Promise<APIResponse> {
    try {
      const prompt = `请生成10张哲学思想卡片，每张卡片包含以下信息：
      - 标题：哲学概念或思想的名称
      - 内容：深入浅出的解释（100-200字）
      - 作者：相关哲学家
      - 分类：哲学流派或领域
      - 引言：经典哲学语录（可选）
      
      请以JSON格式返回，格式如下：
      [
        {
          "id": "唯一标识",
          "title": "标题",
          "content": "内容解释",
          "author": "哲学家",
          "category": "分类",
          "quote": "经典语录",
          "createdAt": "当前日期"
        }
      ]
      
      请确保内容具有教育价值且通俗易懂。`;

      const response = await fetch(`${this._baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${this._apiKey}`,
          'User-Agent': 'https://api.302.ai/v1/chat/completions',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this._model,
          messages: [
            { role: 'user', content: prompt }
          ] as ChatCompletionMessage[],
        }),
      });

      if (!response.ok) {
        console.warn('API调用失败，使用模拟数据', response.status);
        // API调用失败时回退到模拟数据
        return {
          success: true,
          data: mockPhilosophyCards,
        };
      }

      const data: ChatCompletionResponse = await response.json();
      const content = data.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error('API返回内容为空');
      }

      // 尝试解析JSON内容
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        console.warn('无法解析API返回的JSON，使用模拟数据');
        return {
          success: true,
          data: mockPhilosophyCards,
        };
      }

      const parsedCards: PhilosophyCard[] = JSON.parse(jsonMatch[0]);
      
      // 验证数据格式并添加缺失字段
      const validatedCards = parsedCards.map((card, index) => ({
        id: card.id || `card-${Date.now()}-${index}`,
        title: card.title || '哲学思考',
        content: card.content || '深入思考人生的意义和价值。',
        author: card.author || '哲学家',
        category: card.category || '哲学思辨',
        quote: card.quote,
        backgroundColor: `hsl(${(index * 137.5) % 360}, 70%, 85%)`,
        createdAt: new Date().toISOString(),
      }));

      return {
        success: true,
        data: validatedCards.slice(0, 10), // 确保只返回10张卡片
      };

    } catch (error) {
      console.error('Error fetching daily cards:', error);
      // 出错时回退到模拟数据
      return {
        success: true,
        data: mockPhilosophyCards,
        message: '使用备用数据',
      };
    }
  }

  async generatePhilosophyContent(topic: string): Promise<APIResponse> {
    try {
      const prompt = `请基于主题"${topic}"生成一张哲学思想卡片，包含：
      - 标题：与主题相关的哲学概念
      - 内容：深入浅出的解释（100-200字）
      - 作者：相关哲学家
      - 分类：哲学流派
      - 引言：相关的哲学语录
      
      请以JSON格式返回单张卡片数据。`;

      const response = await fetch(`${this._baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${this._apiKey}`,
          'User-Agent': 'https://api.302.ai/v1/chat/completions',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this._model,
          messages: [
            { role: 'user', content: prompt }
          ] as ChatCompletionMessage[],
        }),
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const data: ChatCompletionResponse = await response.json();
      const content = data.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error('API返回内容为空');
      }

      // 解析生成的内容
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('无法解析生成的内容');
      }

      const generatedCard: PhilosophyCard = JSON.parse(jsonMatch[0]);
      
      return {
        success: true,
        data: [{
          ...generatedCard,
          id: `generated-${Date.now()}`,
          backgroundColor: `hsl(${Math.random() * 360}, 70%, 85%)`,
          createdAt: new Date().toISOString(),
        }],
      };

    } catch (error) {
      console.error('Error generating philosophy content:', error);
      return {
        success: false,
        data: [],
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  setApiKey(key: string) {
    this._apiKey = key;
  }

  getConfig() {
    return {
      baseURL: this._baseURL,
      model: this._model,
      hasApiKey: !!this._apiKey
    };
  }
}

export const philosophyAPI = new PhilosophyAPI();