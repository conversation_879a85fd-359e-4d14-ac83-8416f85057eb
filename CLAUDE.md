# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start development server on http://localhost:5173
- `npm run build` - Build for production (runs TypeScript compilation then Vite build)
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint on the codebase

### Development Server Issues
If <PERSON>fari shows "cannot connect to server localhost", try:
1. Kill existing processes: `lsof -ti:5173 | xargs kill -9`
2. Start with host binding: `npm run dev -- --host 0.0.0.0`
3. Access via IP addresses shown in terminal output

## Architecture Overview

### State Management Architecture
This app uses **Zustand** for state management with a single store (`src/store/cardStore.ts`):
- `cards`: Array of philosophy cards loaded from API
- `currentIndex`: Current card position in stack
- `favoriteCards`: Array of card IDs stored in localStorage
- `readCards`: Array of read card IDs, resets daily
- Key actions: `setCards`, `nextCard`, `prevCard`, `toggleFavorite`, `markAsRead`, `resetDaily`

### Component Architecture
```
App.tsx (main container)
├── Header (date, progress, branding)
└── CardStack (main interaction component)
    └── PhilosophyCard (individual card with actions)
```

### Data Flow
1. **App.tsx** fetches cards from `philosophyAPI.getDailyCards()` on mount
2. **CardStack** manages drag interactions and card transitions using Framer Motion
3. **PhilosophyCard** handles individual card actions (favorite, share)
4. **cardStore** persists user interactions to localStorage

### API Service Pattern
- `src/services/api.ts`: Main API class with 302.ai integration placeholder
- `src/services/mockData.ts`: Contains 10 philosophy cards for development
- Currently uses mock data with 1-second delay to simulate API calls
- 302.ai integration ready via `setApiKey()` method

### Animation & Interaction System
- **Framer Motion** powers all animations and drag interactions
- **CardStack drag system**: 
  - Threshold-based swipe detection (100px or 500px/s velocity)
  - Cards stack with depth (scale + y-offset + rotation)
  - Only top card accepts drag interactions
- **Card transitions**: Scale, fade, and slide animations
- **Progress indicators**: Dot-based progress showing current position

### Styling Architecture
- **Tailwind CSS 4.1** with custom configuration
- **Custom color palette**: Primary (purple) and Sage (green) ranges
- **Custom animations**: slideIn, fadeIn, bounceSoft
- **Typography**: Inter font family
- **Responsive design**: Mobile-first approach

### Data Persistence
- `favoriteCards`: Persisted across sessions in localStorage
- `readCards`: Daily reset mechanism for reading progress
- Cards contain: id, title, content, author, category, quote, createdAt

### Type Definitions
Key interfaces in `src/types/index.ts`:
- `PhilosophyCard`: Core card data structure
- `CardStackProps`: Props for card interaction component
- `APIResponse`: Standardized API response format

## TypeScript Configuration Notes
- Uses `verbatimModuleSyntax` - import types with `import type { ... }`
- Strict type checking enabled
- React 19 + TypeScript ~5.8 configuration

## Development Workflow
1. App loads 10 philosophy cards via API service
2. Users interact with cards through drag gestures or button actions
3. Card state (favorites, read status) persists in localStorage
4. Daily reset functionality clears read progress for new sessions