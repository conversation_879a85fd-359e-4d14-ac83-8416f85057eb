# 哲学日课 - Philosophy Cards

一个精美的每日哲学知识卡片网页应用，通过流畅的滑动交互，让用户每天体验10张哲学思想卡片。

## ✨ 特性

- 📱 **响应式设计** - 完美适配手机、平板和桌面端
- 🎯 **滑动交互** - 左右滑动切换卡片，体验流畅
- 🎨 **精美UI** - 渐变背景、优雅动画、现代设计风格
- 💾 **数据持久化** - 本地存储收藏和阅读记录
- 🔄 **每日限制** - 每天10张卡片，培养良好阅读习惯
- ❤️ **收藏功能** - 收藏喜欢的哲学观点
- 📤 **分享功能** - 分享到社交媒体或复制文本
- 🤖 **AI支持** - 集成302.ai API进行智能内容分析

## 🛠️ 技术栈

- **前端框架**: React 19 + TypeScript
- **状态管理**: Zustand
- **样式方案**: Tailwind CSS
- **动画库**: Framer Motion
- **构建工具**: Vite
- **部署平台**: Zeabur

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📁 项目结构

```
philosophy-cards/
├── src/
│   ├── components/
│   │   ├── Card/              # 卡片组件
│   │   ├── CardStack/         # 卡片堆栈组件
│   │   └── Layout/            # 布局组件
│   ├── hooks/                 # 自定义Hook
│   ├── services/              # API服务
│   ├── store/                 # 状态管理
│   ├── types/                 # TypeScript类型定义
│   └── utils/                 # 工具函数
├── public/                    # 静态资源
└── package.json
```

## 🎯 核心功能

### 卡片滑动
- 左右滑动切换卡片
- 拖拽手势支持
- 流畅的动画过渡
- 进度指示器

### 数据管理
- 本地存储收藏列表
- 阅读进度追踪
- 每日重置机制

### 响应式设计
- 移动优先设计
- 自适应布局
- 触摸友好的交互

## 🔧 配置

### 环境变量
创建 `.env` 文件：
```
VITE_API_BASE_URL=https://302.ai
VITE_API_KEY=your_api_key_here
```

### 302.ai API集成
在 `src/services/api.ts` 中配置您的API密钥：
```typescript
philosophyAPI.setApiKey('your_api_key');
```

## 🚀 部署到Zeabur

1. 将代码推送到GitHub仓库
2. 在Zeabur控制台创建新项目
3. 连接GitHub仓库
4. 选择本项目并部署
5. 配置环境变量（如果需要）

## 📱 使用方法

1. 打开应用，查看今日的10张哲学卡片
2. 左右滑动或拖拽切换卡片
3. 点击❤️收藏喜欢的内容
4. 点击📤分享到社交媒体
5. 每天回来获取新的哲学思考

## 🎨 设计理念

这个应用的设计灵感来自于古典哲学的深邃与现代交互的简洁。我们希望通过精美的视觉设计和流畅的交互体验，让用户在忙碌的日常生活中，能够找到片刻的哲学思考时光。

每张卡片都经过精心设计，包含：
- **标题** - 哲学概念的核心
- **内容** - 深入浅出的解释
- **引言** - 经典哲学语录
- **作者** - 哲学家信息
- **分类** - 哲学流派标签

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
